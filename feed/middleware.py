from django.utils import translation
from django.conf import settings
from django.shortcuts import redirect
from django.contrib import messages


class EmployerIsolationMiddleware:
    """
    Middleware to ensure employees can only access data from their own employer.
    This provides multi-tenant data isolation at the request level and handles
    automatic authentication redirects.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # URLs that don't require authentication
        self.public_urls = [
            '/admin/',
            '/signin/',
            '/signout/',
            '/register/',
            '/static/',
            '/media/',
            '/i18n/',
        ]
        # URLs that require authentication but don't need employer isolation
        self.auth_exempt_urls = [
            '/admin/',
            '/signout/',
        ]

    def __call__(self, request):
        # Debug logging for specific URLs
        if request.path == '/debug-auth/':
            print(f"[MIDDLEWARE-DEBUG] Processing {request.path}")
            print(f"[MIDDLEWARE-DEBUG] User authenticated: {request.user.is_authenticated}")

        # Skip middleware for public URLs
        if any(request.path.startswith(url) for url in self.public_urls):
            if request.path == '/debug-auth/':
                print(f"[MIDDLEWARE-DEBUG] Skipping middleware for public URL")
            return self.get_response(request)

        # For all other URLs, require authentication
        if not request.user.is_authenticated:
            # Store the current URL to redirect back after login
            next_url = request.get_full_path()
            return redirect(f'/signin/?next={next_url}')

        # Skip employer isolation for certain authenticated URLs
        if any(request.path.startswith(url) for url in self.auth_exempt_urls):
            return self.get_response(request)

        # Skip for superusers
        if request.user.is_superuser:
            return self.get_response(request)

        # Check if user has an associated employee record
        try:
            employee = request.user.employee

            # Check if employee is active
            if employee.status != 'Active':
                from django.contrib.auth import logout
                logout(request)
                messages.error(request, "Your account is inactive. Please contact support.")
                return redirect('signin')

            # Store employer_id and employer object in request for easy access in views
            request.employer_id = employee.employer_id.employer_id
            request.employer = employee.employer_id  # Store the Employer object
            request.employee = employee

            if request.path == '/debug-auth/':
                print(f"[MIDDLEWARE-DEBUG] Set employer_id: {request.employer_id}")

        except AttributeError:
            # User doesn't have an employee record
            from django.contrib.auth import logout
            logout(request)
            messages.error(request, "Your account is not properly configured. Please contact support.")
            return redirect('signin')

        response = self.get_response(request)
        return response


class ForceLanguageMiddleware:
    """
    Custom middleware to ensure language is properly activated in Docker environments.
    This middleware checks for language preferences in session and cookies and 
    activates the language for each request.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Get language from various sources
        language = self.get_language_from_request(request)
        
        if language:
            print(f"[LANGUAGE-MIDDLEWARE] Activating language: {language}")
            translation.activate(language)
            # Set the language code in the request for templates
            request.LANGUAGE_CODE = language
        else:
            # Use default language
            print(f"[LANGUAGE-MIDDLEWARE] Using default language: {settings.LANGUAGE_CODE}")
            translation.activate(settings.LANGUAGE_CODE)
            request.LANGUAGE_CODE = settings.LANGUAGE_CODE

        response = self.get_response(request)
        
        # Deactivate language after request
        translation.deactivate()
        
        return response

    def get_language_from_request(self, request):
        """
        Get language preference from request in order of priority:
        1. Session
        2. Cookie
        3. Default language
        """
        # Check session first (highest priority)
        language = request.session.get('django_language')
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in session: {language}")
            return language
            
        language = request.session.get('_language')
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in session (_language): {language}")
            return language
            
        language = request.session.get(settings.LANGUAGE_COOKIE_NAME)
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in session (cookie name): {language}")
            return language

        # Check cookies
        language = request.COOKIES.get('django_language')
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in cookie: {language}")
            # Also set it in session for future requests
            request.session['django_language'] = language
            return language
            
        language = request.COOKIES.get(settings.LANGUAGE_COOKIE_NAME)
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in cookie (settings): {language}")
            # Also set it in session for future requests
            request.session['django_language'] = language
            return language

        # No language preference found
        print(f"[LANGUAGE-MIDDLEWARE] No language preference found")
        return None

    def is_valid_language(self, language):
        """Check if the language is in the list of available languages"""
        available_languages = [lang[0] for lang in settings.LANGUAGES]
        return language in available_languages
