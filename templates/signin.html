{% extends 'main.html' %}
{% block content %}
{% load static %}
{% load i18n %}

<div class="signin-container">
    <div class="signin-card">
        <div class="signin-header">
            <h1 class="signin-title">{% trans "Welcome Back" %}</h1>
            <p class="signin-subtitle">{% trans "Sign in to your account" %}</p>
        </div>

        <form method="post" class="signin-form" id="signinForm">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="email" class="form-label">{% trans "Email Address" %}</label>
                <div class="form-input-wrapper">
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="{% trans 'Enter your email' %}"
                        required
                        autocomplete="email"
                    >
                    <div class="form-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                            <polyline points="22,6 12,13 2,6"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">{% trans "Password" %}</label>
                <div class="form-input-wrapper">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="{% trans 'Enter your password' %}"
                        required
                        autocomplete="current-password"
                    >
                    <div class="form-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                    </div>
                    <button type="button" class="password-toggle" id="passwordToggle">
                        <svg class="eye-open" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                        <svg class="eye-closed" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                            <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="form-options">
                <label class="remember-me">
                    <input type="checkbox" name="remember_me" id="remember_me">
                    <span class="checkmark"></span>
                    <span class="remember-text">{% trans "Remember me" %}</span>
                </label>
                <a href="#" class="forgot-password">
                    {% trans "Forgot password?" %}
                </a>
            </div>

            <button type="submit" class="signin-button">
                <span class="button-text">{% trans "Sign In" %}</span>
                <div class="button-spinner" style="display: none;">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>

        <div class="signin-footer">
            <p class="contact-text">
                {% trans "Don't have an account?" %}
                <a href="#" class="contact-link">
                    {% trans "Contact us" %}
                </a>
            </p>
        </div>
    </div>
</div>

<style>
    .signin-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .signin-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 420px;
        position: relative;
        overflow: hidden;
    }

    .signin-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .signin-header {
        text-align: center;
        margin-bottom: 32px;
    }

    .signin-title {
        font-size: 28px;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 8px 0;
        letter-spacing: -0.5px;
    }

    .signin-subtitle {
        font-size: 16px;
        color: #666;
        margin: 0;
        font-weight: 400;
    }

    .signin-form {
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin: 0;
    }

    .form-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
    }

    .form-input {
        width: 100%;
        padding: 14px 16px 14px 48px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 16px;
        background: #fff;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-input::placeholder {
        color: #9ca3af;
    }

    .form-icon {
        position: absolute;
        left: 16px;
        color: #9ca3af;
        pointer-events: none;
        z-index: 1;
    }

    .password-toggle {
        position: absolute;
        right: 16px;
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        padding: 0;
        z-index: 2;
        transition: color 0.2s ease;
    }

    .password-toggle:hover {
        color: #667eea;
    }

    .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: -8px;
    }

    .remember-me {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-size: 14px;
        color: #374151;
    }

    .remember-me input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        width: 18px;
        height: 18px;
        border: 2px solid #e5e7eb;
        border-radius: 4px;
        position: relative;
        transition: all 0.2s ease;
    }

    .remember-me input[type="checkbox"]:checked + .checkmark {
        background: #667eea;
        border-color: #667eea;
    }

    .remember-me input[type="checkbox"]:checked + .checkmark::after {
        content: '';
        position: absolute;
        left: 5px;
        top: 2px;
        width: 4px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .forgot-password {
        font-size: 14px;
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .forgot-password:hover {
        color: #5a6fd8;
        text-decoration: underline;
    }

    .signin-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 16px 24px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        margin-top: 8px;
    }

    .signin-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    .signin-button:active {
        transform: translateY(0);
    }

    .signin-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    .button-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top: 2px solid white;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .signin-footer {
        text-align: center;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid #e5e7eb;
    }

    .contact-text {
        font-size: 14px;
        color: #666;
        margin: 0;
    }

    .contact-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.2s ease;
    }

    .contact-link:hover {
        color: #5a6fd8;
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 480px) {
        .signin-container {
            padding: 16px;
        }

        .signin-card {
            padding: 32px 24px;
        }

        .signin-title {
            font-size: 24px;
        }

        .form-input {
            padding: 12px 16px 12px 44px;
            font-size: 16px;
        }

        .form-options {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
        }
    }

    /* Error states */
    .form-input.error {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .error-message {
        color: #ef4444;
        font-size: 14px;
        margin-top: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    /* Success states */
    .form-input.success {
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const eyeOpen = passwordToggle.querySelector('.eye-open');
    const eyeClosed = passwordToggle.querySelector('.eye-closed');
    const form = document.getElementById('signinForm');
    const submitButton = form.querySelector('.signin-button');
    const buttonText = submitButton.querySelector('.button-text');
    const buttonSpinner = submitButton.querySelector('.button-spinner');

    // Password toggle functionality
    passwordToggle.addEventListener('click', function() {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeOpen.style.display = 'none';
            eyeClosed.style.display = 'block';
        } else {
            passwordInput.type = 'password';
            eyeOpen.style.display = 'block';
            eyeClosed.style.display = 'none';
        }
    });

    // Form submission with loading state
    form.addEventListener('submit', function(e) {
        submitButton.disabled = true;
        buttonText.style.display = 'none';
        buttonSpinner.style.display = 'block';
        
        // Re-enable button after 3 seconds in case of error
        setTimeout(() => {
            submitButton.disabled = false;
            buttonText.style.display = 'block';
            buttonSpinner.style.display = 'none';
        }, 3000);
    });

    // Real-time validation
    const inputs = form.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateInput(this);
            }
        });
    });

    function validateInput(input) {
        const errorElement = input.parentNode.parentNode.querySelector('.error-message');
        
        // Remove existing error
        if (errorElement) {
            errorElement.remove();
        }
        
        input.classList.remove('error', 'success');

        let isValid = true;
        let errorMessage = '';

        if (input.type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(input.value) && input.value !== '') {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
        }

        if (input.type === 'password' || input.type === 'text' && input.id === 'password') {
            if (input.value.length < 6 && input.value !== '') {
                isValid = false;
                errorMessage = 'Password must be at least 6 characters';
            }
        }

        if (!isValid) {
            input.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                ${errorMessage}
            `;
            input.parentNode.parentNode.appendChild(errorDiv);
        } else if (input.value !== '') {
            input.classList.add('success');
        }
    }

    // Auto-focus first input
    document.getElementById('email').focus();
});
</script>

{% endblock %}