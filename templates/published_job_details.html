{% extends 'main.html' %}
{% block content %}
{% load static %}
{% load initials_avatar %}
{% load i18n %}

<style>
  /* Toast Notification Styles */
  .toast {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    background-color: white;
    border-left: 4px solid #10b981; /* Success green */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    pointer-events: none;
    border-radius: 4px;
    overflow: hidden;
  }

  .toast.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  .toast.error {
    border-left-color: #ef4444; /* Error red */
  }

  .toast-header {
    padding: 0.75rem 1rem;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }

  .toast-body {
    padding: 1rem;
  }

  .toast-close {
    cursor: pointer;
    background: none;
    border: none;
    font-size: 1.25rem;
  }
</style>

<div class="container mx-auto px-4 py-8">
  <!-- Toast Notification -->
  <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header d-flex justify-content-between align-items-center">
      <strong class="toast-title">{% trans "Notification" %}</strong>
      <button type="button" class="btn-close toast-close" aria-label="{ % trans 'Close' %}"></button>
    </div>
    <div class="toast-body">
      <span class="toast-message"></span>
    </div>
  </div>
  <!-- Position Header -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="row">
      <div class="col-md-8">
        <h1 class="text-2xl font-bold text-gray-800">
          {{ vacancy.vacancy_title }}
        </h1>
        <p class="text-gray-800">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-50">
            {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}
          </span>
          |
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-50">
            {{ vacancy.vacancy_status }}
          </span>
          |
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-800">
            {% trans "Total Applicants:" %}
            <span class="ml-2 font-bold">{{ application_count }}</span>
          </span>
        </p>
        <h4 class="text-base text-gray-500">
          {% trans "Published At:" %}
          <span data-toggle="tooltip" title="{{ vacancy.days_open }} Days Ago">
            {{ vacancy.vacancy_creation_date|date:"F j, Y" }}
          </span>
        </h4>

        <script>
            $(document).ready(function () {
              $('[data-toggle="tooltip"]').tooltip();
            });
        </script>
      </div>
      <!-- Control Buttons Section -->
      <div class="col-md-4 text-right">
        <div class="d-flex flex-column gap-2 ml-auto">
          <!-- Communications Button -->
          <button
            type="button"
            id="commBtn"
            class="btn btn-info d-flex align-items-center"
            data-bs-toggle="modal"
            data-bs-target="#communicationModal"
          >
            <i class="bi bi-chat-left-text mx-3"></i>
            {% trans "Bulk Communication" %}
          </button>
          <!-- Talent Request Button -->
          <button
            type="button"
            id="requestTalentButton"
            class="btn btn-success d-flex align-items-center"
            data-bs-toggle="modal"
            {% if vacancy.vacancy_status == 'Archived' %}disabled{% endif %}
            data-bs-target="#talentRequestModal"
          >
            <i class="bi bi-person-add mx-3"></i>
            {% trans "Expert Support Options" %}
          </button>

          <!-- Post this job on LinkedIn Button -->
          <button
            type="button"
            id="postLinkedInButton"
            class="btn btn-primary d-flex align-items-center"
            data-bs-toggle="modal"
            data-bs-target="#postLinkedInModal"
          >
            <i class="bi bi-linkedin mx-3"></i>
            {% trans "Post on LinkedIn" %}
          </button>

          <!-- Change the Status Button -->
          <button
            type="button"
            id="chngStatusBtn"
            class="btn btn-danger d-flex align-items-center"
            data-bs-toggle="modal"
            data-bs-target="#changeStatusModal"
          >
            <i class="bi bi-gear mx-3"></i>
            {% trans "Change Vacancy Status" %}
          </button>

        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="grid grid-cols-1 gap-6 mb-6">
    <!-- Applicants Over Time Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">
          {% trans "Applicants Over Time" %}
        </h2>
      </div>
      <canvas id="applicantsLineChart" height="60"></canvas>
    </div>
  </div>

  <!-- Secondary Charts Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Job Portal Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">
        {% trans "Number of Applicants by Job Portal" %}
      </h2>
      <canvas id="jobPortalBarChart" height="200"></canvas>
    </div>

    <!-- Applicant Status Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">
        {% trans "Distribution of Applicants by Status" %}
      </h2>
      <div class="flex justify-center">
        <div style="max-width: 450px; max-height: 450px">
          <canvas id="statusPieChart"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Applicants Table -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-800">{% trans "Top Applicants" %}</h2>
      <a
        href="/people/?position={{ all_applicants_url }}&page=1"
        class="btn btn-purple text-white"
        style="background-color: #6b46c1; border-color: #6b46c1; hover:bg-purple-700;"
      >
        <i class="bi bi-people-fill mx-1"></i> {% trans "View All Applicants" %}
      </a>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-x divide-gray-200 border">
        <!-- Table Headers -->
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Name" %}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Status" %}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Score" %}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {% trans "Actions" %}
            </th>
          </tr>
        </thead>
        <!-- Table Body -->
        <tbody class="bg-white divide-y divide-x divide-gray-200">
          {% for applicant in top_applicants %}
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div
                  style="background-color: {{ applicant.candidate_id.avatar_bg_color }};
                         width: 40px; height: 40px; border-radius: 50%;
                         display: flex; align-items: center; justify-content: center;
                         color: white; font-weight: bold;"
                >
                  {{ applicant.candidate_id.candidate_firstname|slice:":1" }}{{ applicant.candidate_id.candidate_lastname|slice:":1" }}
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    {{ applicant.candidate_id.candidate_firstname }} {{ applicant.candidate_id.candidate_lastname }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ applicant.candidate_id.candidate_email }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                {{ applicant.application_state }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <div class="flex items-center">
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div class="h-2.5 rounded-full applicant-score-bar"
                       style="width: {{ applicant.score }}%; background-color: #3b82f6;"
                       data-score="{{ applicant.score }}">
                  </div>
                </div>
                {% if applicant.score != -1 %}
                <span class="ml-2 text-sm font-medium text-gray-900">
                  {{ applicant.score }}%
                </span>
                {% else %}
                <span class="ml-2 text-sm font-medium text-gray-900">
                  {% trans "Not Rated" %}
                </span>
                {% endif %}

              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
              <a
                href="{% url 'application' application_id=applicant.application_id %}"
                class="text-indigo-600 hover:text-indigo-900 mr-2"
              >
                {% trans "View" %}
              </a>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Talent Request Modal -->
<div class="modal fade" id="talentRequestModal" tabindex="-1" aria-labelledby="talentRequestModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="talentRequestModalLabel">{% trans "Request Support From Experts" %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="text-gray-700">
          {% trans "We can provide vetted candidates from our talent pool, or help you during the technical interviews to pick best fit for your expectations." %}
        </p>
        <form id="talentRequestForm" method="POST" action="{% url 'insert_talent_request' %}">
          {% csrf_token %}
          <div class="mb-3">
            <label for="talentRequestField" class="form-label">{% trans "Enter Details" %}</label>
            <textarea
              class="form-control"
              id="talentRequestField"
              name="request_notes"
              maxlength="500"
              placeholder="Enter your additional request notes (optional)"
            ></textarea>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
            <button type="submit" class="btn btn-primary" name="request_type" value="candidate_req">{% trans "Request Candidates" %}</button>
            <button type="submit" class="btn btn-primary" name="request_type" value="interview_expert_req">{% trans "Request Interview Help" %}</button>
          </div>
          <input type="hidden" name="vacancy_id" value="{{ vacancy.vacancy_id }}" />
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Communication Modal -->
<div class="modal fade" id="communicationModal" tabindex="-1" aria-labelledby="communicationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="communicationModalLabel">{% trans "Send Bulk Mail to Applicants" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-gray-700">
                    {% trans "Use this form to send bulk emails to applicants based on their application statuses." %}
                </p>
                <form id="bulkMailForm" method="POST" action="{% url 'send_bulk_mails' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="applicationStatus" class="form-label">{% trans "Select Application Status" %}</label>
                        <select
                            class="form-select"
                            id="applicationStatus"
                            name="application_status"
                            required
                        >
                            <option value="" disabled selected>{% trans "Select a status" %}</option>
                            {% for state in applications_by_state %}
                            <option value="{{ state.application_state }}">{{ state.application_state }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="emailSubject" class="form-label">{% trans "Email Subject" %}</label>
                        <input
                            type="text"
                            class="form-control"
                            id="emailSubject"
                            name="email_subject"
                            maxlength="100"
                            placeholder="Enter email subject"
                            required
                        />
                    </div>
                    <div class="mb-3">
                        <label for="internalNotes" class="form-label">{% trans "Internal Notes" %} <small class="text-muted">{% trans "(visible only to recruiters)" %}</small></label>
                        <textarea
                            class="form-control"
                            id="internalNotes"
                            name="internal_notes"
                            rows="3"
                            maxlength="500"
                            placeholder="{% trans 'Enter internal notes for your team (optional)' %}"
                        ></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="emailBody" class="form-label">{% trans "Email Body" %} <small class="text-muted">{% trans "(sent to candidates)" %}</small></label>
                        <textarea
                            class="form-control"
                            id="emailBody"
                            name="email_body"
                            rows="5"
                            maxlength="1000"
                            placeholder="{% trans 'Enter your email message' %}"
                            required
                        ></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input
                            type="checkbox"
                            class="form-check-input"
                            id="notifyCandidates"
                            name="notify_candidates"
                            checked
                        />
                        <label class="form-check-label" for="notifyCandidates">{% trans "Send notification emails to candidates" %}</label>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" id="sendBulkEmailsBtn" class="btn btn-primary">{% trans "Send Emails" %}</button>
                    </div>
                    <input type="hidden" name="vacancy_id" value="{{ vacancy.vacancy_id }}" />
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Post on LinkedIn Modal -->
<div class="modal fade" id="postLinkedInModal" tabindex="-1" aria-labelledby="postLinkedInModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="postLinkedInModalLabel">{% trans "Post this job on LinkedIn" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <!-- Tutorial with copy-able values of the job to post on Linkedin step by step -->
              <p>
                  1. Go to <a href="https://www.linkedin.com/job-posting/v2/" target="_blank">LinkedIn Job Posting Page</a> <br>
                  2. Copy and Paste the Job Title.
              </p>

              <!-- Copy to Clipboard Job Title -->
              <div class="d-flex align-items-center gap-2 mb-3">
                  <div class="input-group">
                      <input type="text" class="form-control" value="{{ vacancy.vacancy_title }}" readonly>
                      <button class="btn btn-primary d-flex align-items-center gap-2" onclick="copyToClipboard('{{ vacancy.vacancy_title }}', event)">
                          <i class="bi bi-clipboard"></i>
                          {% trans "Copy to Clipboard" %}
                      </button>
                  </div>
              </div>

              <p> 
              3. Select the "Use my own description" option. <br>
              4. Click on the Pencil icon next to the {{ vacancy.vacancy_title }} to edit the location, job type, and other options as needed. <br>
              <span class="text-sm text-gray-500"> Here are some of the options you selected while creating the job. </span> <br>
              <div class="d-flex flex-wrap gap-2 mt-2">

                <div class="chip bg-light border rounded-pill px-3 py-1">{{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}</div>
                <div class="chip bg-light border rounded-pill px-3 py-1">{{ vacancy.work_schedule }}</div>
                <div class="chip bg-light border rounded-pill px-3 py-1">{{ vacancy.office_schedule }}</div>

              </div> <br>

              5. Copy and Paste the Job Description. <br> <span class="text-sm text-gray-500"> Note: There may be a placeholder text on the LinkedIn editor, delete it before pasting.</span>
              </p>

              <!-- Copy to Clipboard Job Description -->
              <div class="d-flex align-items-center gap-2 mb-3">
                  <div class="input-group">
                      <div class="form-control overflow-auto" style="height: 400px; white-space: pre-wrap;" readonly>
                          <div id="job_desc_safe">{{ vacancy.vacancy_job_description|safe }}</div>
                      </div>
                      <!-- To fix, it currently doesn't copy the style of the text perfectly. -->
                      <button class="btn btn-primary d-flex align-items-center gap-2" onclick="copyToClipboard(document.getElementById('job_desc_safe').textContent, event)">
                          <i class="bi bi-clipboard"></i>
                          {% trans "Copy to Clipboard" %}
                      </button>
                  </div>
              </div>

              <p>
              6. Click on "Continue" Button. <br>
              7. From the displayed options, find the "Manage applicants" option and click to the pencil icon on it to edit. <br>
              8. Change the "Manage Applications" field from "On LinkedIn" to "On an External Website". <br>
              9. Copy Application URL provided below and Paste it to the Website address field.
              </p>

              <!-- Applicatio URL from workloupe as https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=linkedin -->

              <!-- Copy to Clipboard Application URL -->
              <div class="d-flex align-items-center gap-2 mb-3">
                  <div class="input-group">
                      <input type="text" class="form-control" value="https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=linkedin" readonly>
                      <button class="btn btn-primary d-flex align-items-center gap-2" onclick="copyToClipboard('https://workloupe.com/apply/{{ vacancy.vacancy_id }}?source=linkedin', event)">
                          <i class="bi bi-clipboard"></i>
                          {% trans "Copy to Clipboard" %}
                      </button>
                  </div>
              </div>

              <p>
              10. Adjust the other settings on this page as you prefer, then click on "Continue" Button. <br>
              11. On the Ideal Qualifications section, you can review the auto-generated qualifications and make any necessary changes as you prefer. (You may prefer to delete all of them and add your own, or leave empty) <br>
              <span class="text-sm text-gray-500"> For your convenience, these were the skills you added during the creation of this job: </span> <br>
              <div class="d-flex flex-wrap gap-2 mt-2">
                {% for skill in vacancy.skills %}
                <div class="chip bg-light border rounded-pill px-3 py-1">{{ skill }}</div>
                {% endfor %}
              </div>
              </p>

              <p>
              12. if you haven't already, confirm your identity by using your work email. <br>
              13. Based on your preferences, you can select the free job posting, or a promoted one. (We strongly suggest using the free option as the promoted one can cost more than expected over the time.) <br>
              14. Click on "Post Job" Button. <br>
              15. You are done! Your job is now live on LinkedIn.
              </p>

            </div> 
        </div>
    </div>
</div>

<!-- Change Status Modal -->
<div class="modal fade" id="changeStatusModal" tabindex="-1" aria-labelledby="changeStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeStatusModalLabel">{% trans "Change Vacancy Status" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-gray-700">
                    {% trans "Current Status:" %}
                    <span class="badge {% if vacancy.vacancy_status == 'Active' %}bg-success{% elif vacancy.vacancy_status == 'On-Hold' %}bg-warning{% else %}bg-danger{% endif %}">
                        {{ vacancy.vacancy_status }}
                    </span>
                </p>
                <p class="text-gray-700">
                    <strong>{% trans "Note:" %}</strong> {% trans "Changing the status will affect the visibility of the vacancy." %}
                </p>
                <ul class="list-disc pl-5 text-gray-700">
                    <li><strong>Archived:</strong> {% trans "The vacancy will no longer exist on boards and be closed but be accesible internally." %}</li>
                    <li><strong>On-Hold:</strong> {% trans "The vacancy will stop accepting new applications until changed." %}</li>
                    <li><strong>Active:</strong> {% trans "The vacancy will be re-opened for new applications." %}</li>
                    <li><strong>Deleted:</strong> {% trans "The vacancy will be permanently deleted. This action cannot be undone." %}</li>
                </ul>
                <form id="changeStatusForm" method="POST" onsubmit="handleStatusChange(event)">
                    {% csrf_token %}
                    <div class="mb-3">
                        <!-- Status Selection -->
                        <label for="vacancyStatus" class="form-label font-bold text-lg text-gray-800">{% trans "Select New Status" %}</label>
                        <select
                            class="form-select border-2 border-blue-500 rounded-lg p-3 text-gray-700 text-lg font-medium focus:ring-2 focus:ring-blue-300 focus:outline-none"
                            id="new_vacancyStatus"
                            name="new_vacancyStatus"
                            required
                        >
                            <option value="Active" {% if vacancy.vacancy_status == 'Active' %}selected{% endif %}>
                                Active
                            </option>
                            <option value="On-Hold" {% if vacancy.vacancy_status == 'On-Hold' %}selected{% endif %}>
                                On-Hold
                            </option>
                            <option value="Archived" {% if vacancy.vacancy_status == 'Archived' %}selected{% endif %}>
                                Archived
                            </option>
                            <option value="Deleted" {% if vacancy.vacancy_status == 'Deleted' %}selected{% endif %}>
                                Deleted
                            </option>
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" class="btn btn-primary">{% trans "Confirm Status" %}</button>
                    </div>
                    <input type="hidden" name="vacancy_id" value="{{ vacancy.vacancy_id }}" />
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
      // Function to calculate color based on score (same as before)
      function getColorForScore(score) {
        // Normalize score to 0-1 range
        const normalized = score / 100;

        // Define color stops
        const red = { r: 239, g: 68, b: 68 };    // #ef4444
        const yellow = { r: 234, g: 179, b: 8 };  // #eab308
        const green = { r: 34, g: 197, b: 94 };   // #22c55e

        let r, g, b;

        if (normalized <= 0.5) {
          // Transition from red to yellow
          const factor = normalized * 2;
          r = red.r + (yellow.r - red.r) * factor;
          g = red.g + (yellow.g - red.g) * factor;
          b = red.b + (yellow.b - red.b) * factor;
        } else {
          // Transition from yellow to green
          const factor = (normalized - 0.5) * 2;
          r = yellow.r + (green.r - yellow.r) * factor;
          g = yellow.g + (green.g - yellow.g) * factor;
          b = yellow.b + (green.b - yellow.b) * factor;
        }

        return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
      }

      // Apply color to all applicant score bars
      const applicantScoreBars = document.querySelectorAll('.applicant-score-bar');
      applicantScoreBars.forEach(bar => {
        const score = parseInt(bar.getAttribute('data-score'));
        if (!isNaN(score)) {
          bar.style.backgroundColor = getColorForScore(score);
        }
      });
    });
    function handleStatusChange(event) {
        event.preventDefault();
        const form = event.target;
        const newStatus = document.getElementById('new_vacancyStatus').value;
        const vacancyId = "{{ vacancy.vacancy_id }}";

        updateVacancyStatus(vacancyId, newStatus);
    }

    function copyToClipboard(text, event) {
        navigator.clipboard.writeText(text)
            .then(() => {
                console.log('Copied to clipboard:', text);
                // Make the button that this onclick function was used on green
                event.target.classList.add('btn-success');
                // Button text should be replaced by a tick
                event.target.innerHTML = '<i class="bi bi-check"></i>';
            })
            .catch(err => {
                console.error('Failed to copy: ', err);
            });
    }
</script>

<script>
  // Chart Data Configuration
  const applicantsOverTime = {
    labels: [{% for week in applicants_over_time %}"{{ week.week|date:'M d' }}",{% endfor %}],
    datasets: [{
      label: "Applicants",
      data: [{% for week in applicants_over_time %}{{ week.count }},{% endfor %}],
      borderColor: "rgb(59, 130, 246)",
      backgroundColor: "rgba(59, 130, 246, 0.1)",
      tension: 0.4,
      fill: true,
    }]
  };

  const jobPortalData = {
    labels: [{% for source in applications_by_source %}"{{ source.application_source }}",{% endfor %}],
    datasets: [{
      label: "Applicants",
      data: [{% for source in applications_by_source %}"{{ source.count }}",{% endfor %}],
      backgroundColor: [
        "rgba(59, 130, 246, 0.8)",
        "rgba(16, 185, 129, 0.8)",
        "rgba(245, 158, 11, 0.8)",
        "rgba(245, 15, 110, 0.8)",
        "rgba(134, 15, 110, 0.8)",
        "rgba(215, 221, 37, 0.8)",
        "rgba(84, 242, 221, 0.8)",
      ],
      borderWidth: 0,
    }]
  };

  const statusData = {
    labels: [{% for source in applications_by_state %}"{{ source.application_state }}",{% endfor %}],
    datasets: [{
      data: [{% for source in applications_by_state %}"{{ source.count }}",{% endfor %}],
      backgroundColor: [
        "rgba(239, 68, 68, 0.8)",
        "rgba(59, 130, 246, 0.8)",
        "rgba(16, 185, 129, 0.8)",
        "rgba(245, 158, 11, 0.8)",
        "rgba(139, 92, 246, 0.8)",
      ],
      borderWidth: 0,
    }]
  };

  // Chart Initializations
  const lineCtx = document.getElementById("applicantsLineChart").getContext("2d");
  new Chart(lineCtx, {
    type: "line",
    data: applicantsOverTime,
    options: {
      responsive: true,
      plugins: { legend: { display: false } },
      scales: {
        y: {
          beginAtZero: true,
          ticks: { precision: 0 }
        }
      }
    }
  });

  const barCtx = document.getElementById("jobPortalBarChart").getContext("2d");
  new Chart(barCtx, {
    type: "bar",
    data: jobPortalData,
    options: {
      responsive: true,
      plugins: { legend: { display: false } },
      scales: {
        y: {
          beginAtZero: true,
          ticks: { precision: 0 }
        }
      }
    }
  });

  const pieCtx = document.getElementById("statusPieChart").getContext("2d");
  new Chart(pieCtx, {
    type: "pie",
    data: statusData,
    options: {
      responsive: true,
      maintainAspectRatio: true,
      plugins: {
        legend: {
          position: "right",
          labels: {
            boxWidth: 15,
            font: { size: 13 }
          }
        }
      }
    }
  });


  function updateVacancyStatus(vacancyId, status) {
    fetch(`/update_vacancy_status/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': '{{ csrf_token }}'
      },
      body: JSON.stringify({ vacancy_id: vacancyId, status: status })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('Failed to update vacancy status.');
      }
    });
  }

  // Toast notification functions
  function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    const toastMessage = document.querySelector('.toast-message');

    // Set the message
    toastMessage.textContent = message;

    // Reset classes first
    toast.className = 'toast';

    // Add appropriate classes
    if (type === 'error') {
      toast.classList.add('error', 'show');
    } else {
      toast.classList.add('show');
    }

    // Auto hide after 5 seconds
    setTimeout(() => {
      toast.className = 'toast';
    }, 5000);
  }

  // Handle bulk email form submission
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize toast close button
    const toastClose = document.querySelector('.toast-close');
    if (toastClose) {
      toastClose.addEventListener('click', function() {
        document.getElementById('toast').className = 'toast';
      });
    }

    // Handle bulk email form submission
    const bulkMailForm = document.getElementById('bulkMailForm');
    if (bulkMailForm) {
      bulkMailForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = document.getElementById('sendBulkEmailsBtn');

        // Disable button and show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';

        // Send the form data via fetch
        fetch('{% url "send_bulk_mails" %}', {
          method: 'POST',
          body: formData,
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          // Re-enable button
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Send Emails';

          if (data.success) {
            // Show success message
            showToast(data.message || 'Emails are being sent in the background.');

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('communicationModal'));
            modal.hide();

            // Reset the form
            bulkMailForm.reset();
          } else {
            // Show error message
            showToast(data.message || 'Failed to send emails. Please try again.', 'error');
          }
        })
        .catch(error => {
          // Re-enable button
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Send Emails';

          // Show error message
          showToast('An error occurred. Please try again.', 'error');
          console.error('Error:', error);
        });
      });
    }
  });
</script>

{% endblock %}